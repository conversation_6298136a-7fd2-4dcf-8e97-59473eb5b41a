import React, { useState } from 'react';
import { CreditCard, DollarSign, ArrowRight } from './icons';
import { Donor } from '../types/donor';

interface DonationFormProps {
  donor: Donor;
  onPaymentInitiated: (amount: number) => void;
  onCancel: () => void;
}

export default function DonationForm({ donor, onPaymentInitiated, onCancel }: DonationFormProps) {
  const [amount, setAmount] = useState<string>('');
  const [customAmount, setCustomAmount] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  const presetAmounts = [10, 25, 50, 100, 250, 500];

  const handleAmountSelect = (value: number) => {
    setAmount(value.toString());
    setCustomAmount('');
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    setAmount(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const donationAmount = parseFloat(amount);
    
    if (donationAmount <= 0) return;
    
    setIsProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      onPaymentInitiated(donationAmount);
      setIsProcessing(false);
    }, 2000);
  };

  const selectedAmount = parseFloat(amount) || 0;

  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 animate-fadeIn">
      {/* Header */}
      <div className="bg-gradient-to-r from-pink-500 to-rose-500 px-6 py-4">
        <h2 className="text-xl font-bold text-white">Make a Donation</h2>
        <p className="text-pink-100 mt-1">to {donor.name}</p>
      </div>

      <form onSubmit={handleSubmit} className="p-6">
        {/* Amount Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Select Amount
          </label>
          <div className="grid grid-cols-3 gap-2 mb-4">
            {presetAmounts.map((preset) => (
              <button
                key={preset}
                type="button"
                onClick={() => handleAmountSelect(preset)}
                className={`py-2 px-3 rounded-lg border text-sm font-medium transition-all duration-200 ${
                  amount === preset.toString()
                    ? 'bg-pink-500 text-white border-pink-500'
                    : 'bg-white text-gray-700 border-gray-300 hover:border-pink-300'
                }`}
              >
                ${preset}
              </button>
            ))}
          </div>
          
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="number"
              placeholder="Custom amount"
              value={customAmount}
              onChange={(e) => handleCustomAmountChange(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              min="1"
              step="0.01"
            />
          </div>
        </div>

        {/* Payment Summary */}
        {selectedAmount > 0 && (
          <div className="bg-gray-50 rounded-xl p-4 mb-6">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Donation Amount:</span>
              <span className="text-2xl font-bold text-gray-900">${selectedAmount.toFixed(2)}</span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            type="submit"
            disabled={selectedAmount <= 0 || isProcessing}
            className="w-full bg-gradient-to-r from-pink-500 to-rose-500 text-white py-3 px-4 rounded-xl font-semibold hover:from-pink-600 hover:to-rose-600 transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Processing...</span>
              </>
            ) : (
              <>
                <CreditCard className="h-5 w-5" />
                <span>Donate ${selectedAmount.toFixed(2)}</span>
                <ArrowRight className="h-5 w-5" />
              </>
            )}
          </button>
          
          <button
            type="button"
            onClick={onCancel}
            className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200 transition-all duration-200"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}
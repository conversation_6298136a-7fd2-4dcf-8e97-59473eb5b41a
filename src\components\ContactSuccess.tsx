import React from 'react';
import { CheckCircle, ArrowLeft, Mail, Clock } from './icons';
import { ContactFormData } from './ContactForm';

interface ContactSuccessProps {
  formData: ContactFormData;
  onBackToHome: () => void;
}

export default function ContactSuccess({ formData, onBackToHome }: ContactSuccessProps) {
  return (
    <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 animate-fadeIn">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-500 to-emerald-500 px-6 py-4">
        <div className="flex items-center">
          <button
            onClick={onBackToHome}
            className="mr-3 p-1 rounded-full hover:bg-green-600 transition-colors duration-200"
          >
            <ArrowLeft className="h-5 w-5 text-white" />
          </button>
          <div>
            <h2 className="text-xl font-bold text-white">Application Submitted!</h2>
            <p className="text-green-100 mt-1">Thank you for your interest</p>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Success Icon */}
        <div className="flex justify-center mb-6">
          <div className="bg-green-100 p-4 rounded-full">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
        </div>

        {/* Success Message */}
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            Thank you, {formData.name}!
          </h3>
          <p className="text-gray-600 leading-relaxed">
            Your application to become a listed donor has been successfully submitted. 
            Our team will review your information and get back to you soon.
          </p>
        </div>

        {/* Application Summary */}
        <div className="bg-gray-50 rounded-xl p-4 mb-6">
          <h4 className="font-semibold text-gray-900 mb-3">Application Summary</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Name:</span>
              <span className="font-medium text-gray-900">{formData.name}</span>
            </div>
            {formData.organization && (
              <div className="flex justify-between">
                <span className="text-gray-600">Organization:</span>
                <span className="font-medium text-gray-900">{formData.organization}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">Email:</span>
              <span className="font-medium text-gray-900">{formData.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Donation Type:</span>
              <span className="font-medium text-gray-900 capitalize">{formData.donationType}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Location:</span>
              <span className="font-medium text-gray-900">{formData.city}, {formData.state}</span>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="space-y-4 mb-6">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-100 p-2 rounded-full mt-1">
              <Mail className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h5 className="font-semibold text-gray-900">Email Confirmation</h5>
              <p className="text-gray-600 text-sm">
                You'll receive a confirmation email at {formData.email} within the next few minutes.
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-orange-100 p-2 rounded-full mt-1">
              <Clock className="h-4 w-4 text-orange-600" />
            </div>
            <div>
              <h5 className="font-semibold text-gray-900">Review Process</h5>
              <p className="text-gray-600 text-sm">
                Our team will review your application within 2-3 business days and contact you with next steps.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-pink-50 rounded-xl p-4 mb-6">
          <h4 className="font-semibold text-gray-900 mb-2">Questions?</h4>
          <p className="text-gray-600 text-sm mb-2">
            If you have any questions about your application or the listing process, feel free to contact us:
          </p>
          <div className="space-y-1 text-sm">
            <p className="text-gray-700">
              <span className="font-medium">Email:</span> <EMAIL>
            </p>
            <p className="text-gray-700">
              <span className="font-medium">Phone:</span> +855 23 123 456
            </p>
          </div>
        </div>

        {/* Back to Home Button */}
        <button
          onClick={onBackToHome}
          className="w-full bg-gradient-to-r from-pink-500 to-rose-500 text-white py-3 px-4 rounded-xl font-semibold hover:from-pink-600 hover:to-rose-600 transition-all duration-200 flex items-center justify-center space-x-2"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Back to Home</span>
        </button>
      </div>
    </div>
  );
}

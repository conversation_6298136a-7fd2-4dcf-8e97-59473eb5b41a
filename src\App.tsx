import React, { useState } from 'react';
import { Heart, Gift, Users } from './components/icons';
import DonorSelector from './components/DonorSelector';
import DonorCard from './components/DonorCard';
import DonationForm from './components/DonationForm';
import PaymentSuccess from './components/PaymentSuccess';
import ContactForm, { ContactFormData } from './components/ContactForm';
import ContactSuccess from './components/ContactSuccess';
import { donors } from './data/donors';
import { Donor } from './types/donor';
import { DonationTransaction } from './types/donation';

type AppState = 'selection' | 'donation' | 'success' | 'contact' | 'contact-success';

function App() {
  const [selectedDonor, setSelectedDonor] = useState<Donor | null>(null);
  const [appState, setAppState] = useState<AppState>('selection');
  const [currentTransaction, setCurrentTransaction] = useState<DonationTransaction | null>(null);
  const [contactFormData, setContactFormData] = useState<ContactFormData | null>(null);

  const handleDonateClick = () => {
    setAppState('donation');
  };

  const handlePaymentInitiated = (amount: number) => {
    if (!selectedDonor) return;

    // Create transaction record
    const transaction: DonationTransaction = {
      id: `TXN-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      donorId: selectedDonor.id,
      amount,
      donationType: selectedDonor.donationType,
      timestamp: new Date(),
      status: 'completed',
      transactionHash: `0x${Math.random().toString(16).substr(2, 64)}`
    };

    setCurrentTransaction(transaction);
    setAppState('success');
  };

  const handleBackToDonors = () => {
    setAppState('selection');
    setCurrentTransaction(null);
  };

  const handleCancelDonation = () => {
    setAppState('selection');
  };

  const handleContactClick = () => {
    setAppState('contact');
  };

  const handleContactSubmit = (formData: ContactFormData) => {
    setContactFormData(formData);
    setAppState('contact-success');
  };

  const handleContactBack = () => {
    setAppState('selection');
  };

  const handleContactSuccessBack = () => {
    setAppState('selection');
    setContactFormData(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-rose-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-md mx-auto px-4 py-6">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <div className="bg-gradient-to-r from-pink-500 to-rose-500 p-3 rounded-full">
                <Heart className="h-6 w-6 text-white" />
              </div>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">CAMBODIA UNITED</h1>
            <p className="text-gray-600 mt-1">Connect with donors in your community</p>

            {/* Get Listed Button - Only show on selection screen */}
            {appState === 'selection' && (
              <div className="mt-4">
                <button
                  onClick={handleContactClick}
                  className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-6 py-2 rounded-full font-medium hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 flex items-center space-x-2 mx-auto"
                >
                  <Users className="h-4 w-4" />
                  <span>Get Listed as a Donor</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-md mx-auto px-4 py-6">
        {/* Donor Selection - Only show on selection screen */}
        {appState === 'selection' && (
          <div className="mb-8">
            <DonorSelector
              donors={donors}
              selectedDonor={selectedDonor}
              onSelectDonor={setSelectedDonor}
            />
          </div>
        )}

        {/* Content based on app state */}
        {appState === 'contact' ? (
          <ContactForm
            onBack={handleContactBack}
            onSubmit={handleContactSubmit}
          />
        ) : appState === 'contact-success' && contactFormData ? (
          <ContactSuccess
            formData={contactFormData}
            onBackToHome={handleContactSuccessBack}
          />
        ) : selectedDonor && appState === 'selection' ? (
          <DonorCard donor={selectedDonor} onDonateClick={handleDonateClick} />
        ) : selectedDonor && appState === 'donation' ? (
          <DonationForm
            donor={selectedDonor}
            onPaymentInitiated={handlePaymentInitiated}
            onCancel={handleCancelDonation}
          />
        ) : selectedDonor && appState === 'success' && currentTransaction ? (
          <PaymentSuccess
            donor={selectedDonor}
            transaction={currentTransaction}
            onBackToDonors={handleBackToDonors}
          />
        ) : appState === 'selection' ? (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 text-center">
            <div className="bg-gray-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Gift className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Select a Donor</h3>
            <p className="text-gray-600 text-sm leading-relaxed">
              Choose a donor from the dropdown above to view their information, QR code, and location details.
            </p>
          </div>
        ) : null}

        {/* Stats Footer - Only show on selection screen */}
        {appState === 'selection' && (
        <div className="mt-8 bg-gradient-to-r from-pink-500 to-rose-500 rounded-2xl p-6 text-white">
          <div className="text-center">
            <div className="flex justify-center space-x-8">
              <div>
                <p className="text-2xl font-bold">{donors.length}</p>
                <p className="text-pink-100 text-sm">Active Donors</p>
              </div>
              <div>
                <p className="text-2xl font-bold">5</p>
                <p className="text-pink-100 text-sm">Donation Types</p>
              </div>
              <div>
                <p className="text-2xl font-bold">24/7</p>
                <p className="text-pink-100 text-sm">Support</p>
              </div>
            </div>
          </div>
        </div>
        )}
      </div>

      {/* Attribution Footer */}
      <div className="max-w-md mx-auto px-4 pb-6">
        <div className="text-center">
          <p className="text-sm text-gray-500">
            Powered by{' '}
            <span className="font-semibold text-gray-700">Key Impact Cambodia</span>
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
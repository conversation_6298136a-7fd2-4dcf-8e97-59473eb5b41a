# Fixing 499 Antivirus Error

## Problem
The error "Failed to load resource: the server responded with a status of 499 (Request has been forbidden by antivirus)" occurs when antivirus software blocks network requests. This can affect:
1. External services like Google Maps
2. Dynamic imports from node_modules (like Lucide React icons)
3. Any HTTP requests that the antivirus considers suspicious

## Solutions Applied

### 1. Content Security Policy (CSP)
Added CSP headers to `index.html` to explicitly allow connections to Google services:
```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://www.google.com https://maps.google.com https://maps.googleapis.com; frame-src 'self' https://www.google.com https://maps.google.com;">
```

### 2. Updated Google Maps URL Format
Changed from the API-style URL to a more standard format:
- **Before**: `https://www.google.com/maps/search/?api=1&query=...`
- **After**: `https://maps.google.com/maps?q=...&hl=en`

### 3. Error Handling with Fallback
Added JavaScript error handling that:
- Tries to open the maps link normally
- If blocked, copies the address to clipboard
- Shows a user-friendly message

### 4. Centralized Icon Imports
Created `src/components/icons.ts` to import all Lucide React icons statically, preventing dynamic imports that antivirus software might block:
```typescript
// All icons imported statically to prevent dynamic loading issues
export { Heart, Gift, MapPin, Phone, Building, CreditCard, ... } from 'lucide-react';
```

### 5. Updated Vite Configuration
Enhanced `vite.config.ts` with:
- Forced optimization of lucide-react
- Manual chunking for better bundling
- Additional CORS headers
```typescript
optimizeDeps: {
  include: ['lucide-react'],
  force: true,
},
build: {
  rollupOptions: {
    output: {
      manualChunks: {
        'lucide-react': ['lucide-react'],
      },
    },
  },
},
```

## Additional Troubleshooting

### If the error persists:

1. **Whitelist the application in your antivirus**:
   - Add `localhost:5174` to your antivirus whitelist
   - Add `*.google.com` and `*.googleapis.com` to allowed domains

2. **Temporarily disable real-time protection**:
   - Test if the issue resolves with antivirus temporarily disabled
   - If so, add permanent exceptions

3. **Browser-specific fixes**:
   - Clear browser cache and cookies
   - Disable browser security extensions temporarily
   - Try in incognito/private mode

4. **Alternative map services**:
   - Consider using OpenStreetMap or other mapping services
   - Implement a simple address display without external links

### 6. Cache Clearing
Cleared Vite cache to ensure fresh builds:
```bash
rm -rf node_modules/.vite
npm run dev
```

## Testing
1. Clear Vite cache: `rm -rf node_modules/.vite`
2. Start the development server: `npm run dev`
3. Open http://localhost:5175/ (or the port shown in terminal)
4. Select a donor and verify:
   - Icons load properly (no 499 errors for fingerprint.js or other icon files)
   - "View on Google Maps" link works or provides fallback
   - All UI elements display correctly

## Production Deployment
When deploying to production, ensure your hosting provider supports:
- Custom headers (for CSP)
- HTTPS (required for clipboard API)
- Proper CORS configuration
